{"name": "sentrycoin-flash-crash-predictor", "version": "4.0.2", "description": "SentryCoin v4.0 - Dual-Strategy Market Classification Engine with Detailed Reporting", "main": "src/index-v4.js", "type": "module", "scripts": {"start": "node src/index-minimal.js", "start:v3": "node src/index.js", "start:v4": "node src/index-v4.js", "start:multi": "node src/multi-symbol-predictor.js", "start:trifecta": "ALGORITHM_VERSION=v3.0 ENABLE_TRIFECTA=true node src/index.js", "start:azure": "node src/index-v4.js", "dev": "node --watch src/index-v4.js", "dev:v3": "node --watch src/index.js", "dev:v4": "node --watch src/index-v4.js", "dev:multi": "node --watch src/multi-symbol-predictor.js", "dev:trifecta": "ALGORITHM_VERSION=v3.0 ENABLE_TRIFECTA=true node --watch src/index.js", "test": "node src/test.js", "test:trifecta": "node trifecta-backtest.js", "test:v4": "node src/test-v4.js", "backtest": "node run-backtest.js", "backtest:fetch": "node run-backtest.js fetch", "backtest:test": "node run-backtest.js test", "backtest:quick": "node run-backtest.js quick", "backtest:trifecta": "node trifecta-backtest.js", "backtest:v4": "node src/backtest-v4.js", "connectivity": "node src/connectivity-test.js", "test:telegram": "node test-telegram-simple.js", "build": "echo 'No build step required for Node.js'", "validation-report": "node view-validation-report.js", "download-reports": "pwsh ./download-detailed-reports.ps1", "reports:session": "curl http://localhost:3000/reports/session", "reports:hourly": "curl http://localhost:3000/reports/hourly", "reports:daily": "curl http://localhost:3000/reports/daily", "reports:list": "curl http://localhost:3000/reports"}, "dependencies": {"ws": "^8.14.2", "node-telegram-bot-api": "^0.64.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "express": "^4.18.2", "cors": "^2.8.5", "socket.io": "^4.7.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["crypto", "flash-crash", "order-book", "quantitative", "trading", "binance", "market-microstructure", "liquidity-analysis"], "author": "Sentry<PERSON><PERSON>n", "license": "MIT", "engines": {"node": ">=18.0.0"}}