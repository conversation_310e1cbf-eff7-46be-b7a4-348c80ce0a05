<!DOCTYPE html>
<html>
<head>
    <title>PumpAlarm Icon Generator</title>
</head>
<body>
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvasApple" width="180" height="180" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    
    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Alarm emoji
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🚨', size/2, size/2);
            
            // Download link
            const link = document.createElement('a');
            link.download = `icon-${size === 180 ? 'apple-touch' : size}.png`;
            link.href = canvas.toDataURL();
            link.textContent = `Download ${size}x${size} icon`;
            link.style.display = 'block';
            link.style.margin = '10px';
            document.body.appendChild(link);
        }
        
        createIcon('canvas192', 192);
        createIcon('canvas512', 512);
        createIcon('canvasApple', 180);
    </script>
</body>
</html>
